/**
 * Payment Test Data Insertion Script
 *
 * This script inserts approximately 500 test records across all 8 payment tables
 * to simulate realistic payment data for testing the payment migration.
 *
 * Distribution:
 * - User Payment Tables: ~250 records (62-63 per table)
 * - Guest Payment Tables: ~250 records (62-63 per table)
 *
 * Usage: npm run seed:payment-data
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Sample data arrays for realistic test data
const sampleUsers = [
  'user_001',
  'user_002',
  'user_003',
  'user_004',
  'user_005',
  'user_006',
  'user_007',
  'user_008',
  'user_009',
  'user_010',
  'user_011',
  'user_012',
  'user_013',
  'user_014',
  'user_015',
  'user_016',
  'user_017',
  'user_018',
  'user_019',
  'user_020',
];

const sampleServices = [
  'service_001',
  'service_002',
  'service_003',
  'service_004',
  'service_005',
  'service_006',
  'service_007',
  'service_008',
  'service_009',
  'service_010',
];

const samplePackages = [
  'package_001',
  'package_002',
  'package_003',
  'package_004',
  'package_005',
  'package_006',
  'package_007',
  'package_008',
  'package_009',
  'package_010',
];

const sampleImmigrationServices = [
  'immigration_001',
  'immigration_002',
  'immigration_003',
  'immigration_004',
  'immigration_005',
  'immigration_006',
  'immigration_007',
  'immigration_008',
  'immigration_009',
  'immigration_010',
];

const sampleTraining = [
  'training_001',
  'training_002',
  'training_003',
  'training_004',
  'training_005',
  'training_006',
  'training_007',
  'training_008',
  'training_009',
  'training_010',
];

const paymentStatuses = ['paid', 'pending', 'failed', 'refunded'];
const progressStatuses = ['Pending', 'Active', 'Inactive'];

const guestNames = [
  'John Smith',
  'Sarah Johnson',
  'Michael Brown',
  'Emma Davis',
  'James Wilson',
  'Olivia Miller',
  'William Garcia',
  'Sophia Martinez',
  'Benjamin Anderson',
  'Isabella Taylor',
  'Lucas Thomas',
  'Mia Jackson',
  'Henry White',
  'Charlotte Harris',
  'Alexander Martin',
  'Amelia Thompson',
  'Daniel Garcia',
  'Harper Lewis',
  'Matthew Robinson',
  'Evelyn Clark',
];

const guestEmails = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

const guestPhones = [
  '+353-1-234-5678',
  '+353-1-234-5679',
  '+353-1-234-5680',
  '+353-1-234-5681',
  '+353-1-234-5682',
  '+353-1-234-5683',
  '+353-1-234-5684',
  '+353-1-234-5685',
  '+353-1-234-5686',
  '+353-1-234-5687',
  '+353-1-234-5688',
  '+353-1-234-5689',
  '+353-1-234-5690',
  '+353-1-234-5691',
  '+353-1-234-5692',
  '+353-1-234-5693',
  '+353-1-234-5694',
  '+353-1-234-5695',
  '+353-1-234-5696',
  '+353-1-234-5697',
];

// Utility functions
function getRandomElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

function getRandomAmount(): number {
  // Generate amounts between €50 and €500 (in cents)
  return Math.floor(Math.random() * 45000) + 5000;
}

function getRandomDate(): Date {
  // Generate dates within the last 6 months
  const now = new Date();
  const sixMonthsAgo = new Date(now.getTime() - 6 * 30 * 24 * 60 * 60 * 1000);
  const randomTime =
    sixMonthsAgo.getTime() +
    Math.random() * (now.getTime() - sixMonthsAgo.getTime());
  return new Date(randomTime);
}

function generateCuid(): string {
  // Simple CUID-like generator for test data
  const timestamp = Date.now().toString(36);
  const randomPart = Math.random().toString(36).substring(2, 8);
  return `cl${timestamp}${randomPart}`;
}

// Data insertion functions
async function insertUserMentorServiceData(count: number) {
  console.log(`Inserting ${count} user_mentor_service records...`);

  for (let i = 0; i < count; i++) {
    const createdAt = getRandomDate();
    const updatedAt = new Date(
      createdAt.getTime() + Math.random() * 24 * 60 * 60 * 1000,
    );

    await prisma.user_mentor_service.create({
      data: {
        id: generateCuid(),
        amount: getRandomAmount(),
        status: getRandomElement(paymentStatuses),
        progress: getRandomElement(progressStatuses) as any,
        userId: getRandomElement(sampleUsers),
        serviceId: getRandomElement(sampleServices),
        createdAt,
        updatedAt,
      },
    });
  }

  console.log(`✅ Inserted ${count} user_mentor_service records`);
}

async function insertGuestMentorServiceData(count: number) {
  console.log(`Inserting ${count} guest_mentor_service records...`);

  for (let i = 0; i < count; i++) {
    const createdAt = getRandomDate();
    const updatedAt = new Date(
      createdAt.getTime() + Math.random() * 24 * 60 * 60 * 1000,
    );
    const guestIndex = i % guestNames.length;

    await prisma.guest_mentor_service.create({
      data: {
        id: generateCuid(),
        amount: getRandomAmount(),
        status: getRandomElement(paymentStatuses),
        name: guestNames[guestIndex],
        email: guestEmails[guestIndex],
        mobile_no: guestPhones[guestIndex],
        progress: getRandomElement(progressStatuses) as any,
        serviceId: getRandomElement(sampleServices),
        createdAt,
        updatedAt,
      },
    });
  }

  console.log(`✅ Inserted ${count} guest_mentor_service records`);
}

async function insertUserPackageData(count: number) {
  console.log(`Inserting ${count} user_package records...`);

  for (let i = 0; i < count; i++) {
    const createdAt = getRandomDate();
    const updatedAt = new Date(
      createdAt.getTime() + Math.random() * 24 * 60 * 60 * 1000,
    );

    await prisma.user_package.create({
      data: {
        id: generateCuid(),
        amount: getRandomAmount(),
        status: getRandomElement(paymentStatuses),
        userId: getRandomElement(sampleUsers),
        packageId: getRandomElement(samplePackages),
        createdAt,
        updatedAt,
      },
    });
  }

  console.log(`✅ Inserted ${count} user_package records`);
}

async function insertGuestPackageData(count: number) {
  console.log(`Inserting ${count} guest_package records...`);

  for (let i = 0; i < count; i++) {
    const createdAt = getRandomDate();
    const updatedAt = new Date(
      createdAt.getTime() + Math.random() * 24 * 60 * 60 * 1000,
    );
    const guestIndex = i % guestNames.length;

    await prisma.guest_package.create({
      data: {
        id: generateCuid(),
        amount: getRandomAmount(),
        status: getRandomElement(paymentStatuses),
        name: guestNames[guestIndex],
        email: guestEmails[guestIndex],
        mobile_no: guestPhones[guestIndex],
        packageId: getRandomElement(samplePackages),
        createdAt,
        updatedAt,
      },
    });
  }

  console.log(`✅ Inserted ${count} guest_package records`);
}

async function insertUserImmigrationServiceData(count: number) {
  console.log(`Inserting ${count} user_immigration_service records...`);

  for (let i = 0; i < count; i++) {
    const createdAt = getRandomDate();
    const updatedAt = new Date(
      createdAt.getTime() + Math.random() * 24 * 60 * 60 * 1000,
    );

    await prisma.user_immigration_service.create({
      data: {
        id: generateCuid(),
        amount: getRandomAmount(),
        status: getRandomElement(paymentStatuses),
        userId: getRandomElement(sampleUsers),
        immigration_serviceId: getRandomElement(sampleImmigrationServices),
        createdAt,
        updatedAt,
      },
    });
  }

  console.log(`✅ Inserted ${count} user_immigration_service records`);
}

async function insertGuestImmigrationServiceData(count: number) {
  console.log(`Inserting ${count} guest_immigration_service records...`);

  for (let i = 0; i < count; i++) {
    const createdAt = getRandomDate();
    const updatedAt = new Date(
      createdAt.getTime() + Math.random() * 24 * 60 * 60 * 1000,
    );
    const guestIndex = i % guestNames.length;

    await prisma.guest_immigration_service.create({
      data: {
        id: generateCuid(),
        amount: getRandomAmount(),
        status: getRandomElement(paymentStatuses),
        name: guestNames[guestIndex],
        email: guestEmails[guestIndex],
        mobile_no: guestPhones[guestIndex],
        immigration_serviceId: getRandomElement(sampleImmigrationServices),
        createdAt,
        updatedAt,
      },
    });
  }

  console.log(`✅ Inserted ${count} guest_immigration_service records`);
}

async function insertUserTrainingData(count: number) {
  console.log(`Inserting ${count} user_training records...`);

  for (let i = 0; i < count; i++) {
    const createdAt = getRandomDate();
    const updatedAt = new Date(
      createdAt.getTime() + Math.random() * 24 * 60 * 60 * 1000,
    );

    await prisma.user_training.create({
      data: {
        id: generateCuid(),
        amount: getRandomAmount(),
        status: getRandomElement(paymentStatuses),
        progress: getRandomElement(progressStatuses) as any,
        userId: getRandomElement(sampleUsers),
        trainingId: getRandomElement(sampleTraining),
        createdAt,
        updatedAt,
      },
    });
  }

  console.log(`✅ Inserted ${count} user_training records`);
}

async function insertGuestTrainingData(count: number) {
  console.log(`Inserting ${count} guest_training records...`);

  for (let i = 0; i < count; i++) {
    const createdAt = getRandomDate();
    const updatedAt = new Date(
      createdAt.getTime() + Math.random() * 24 * 60 * 60 * 1000,
    );
    const guestIndex = i % guestNames.length;

    await prisma.guest_training.create({
      data: {
        id: generateCuid(),
        amount: getRandomAmount(),
        status: getRandomElement(paymentStatuses),
        name: guestNames[guestIndex],
        email: guestEmails[guestIndex],
        mobile_no: guestPhones[guestIndex],
        progress: getRandomElement(progressStatuses) as any,
        trainingId: getRandomElement(sampleTraining),
        createdAt,
        updatedAt,
      },
    });
  }

  console.log(`✅ Inserted ${count} guest_training records`);
}

// Main execution function
async function main() {
  console.log('🚀 Starting payment test data insertion...');
  console.log('📊 Target: ~500 records across 8 payment tables\n');

  try {
    // Check if tables are empty before inserting
    const existingCounts = {
      userMentorService: await prisma.user_mentor_service.count(),
      guestMentorService: await prisma.guest_mentor_service.count(),
      userPackage: await prisma.user_package.count(),
      guestPackage: await prisma.guest_package.count(),
      userImmigrationService: await prisma.user_immigration_service.count(),
      guestImmigrationService: await prisma.guest_immigration_service.count(),
      userTraining: await prisma.user_training.count(),
      guestTraining: await prisma.guest_training.count(),
    };

    const totalExisting = Object.values(existingCounts).reduce(
      (sum, count) => sum + count,
      0,
    );

    if (totalExisting > 0) {
      console.log('⚠️  Existing payment data found:');
      Object.entries(existingCounts).forEach(([table, count]) => {
        if (count > 0) {
          console.log(`   ${table}: ${count} records`);
        }
      });
      console.log(`   Total: ${totalExisting} records\n`);

      console.log(
        '❓ Do you want to continue and add more test data? (This will not delete existing data)',
      );
      console.log(
        '   Press Ctrl+C to cancel, or wait 5 seconds to continue...\n',
      );

      // Wait 5 seconds before continuing
      await new Promise((resolve) => setTimeout(resolve, 5000));
    }

    // Insert data into each table (approximately 62-63 records per table)
    const recordsPerTable = 62;

    // User payment tables
    await insertUserMentorServiceData(recordsPerTable);
    await insertUserPackageData(recordsPerTable);
    await insertUserImmigrationServiceData(recordsPerTable);
    await insertUserTrainingData(recordsPerTable);

    // Guest payment tables
    await insertGuestMentorServiceData(recordsPerTable);
    await insertGuestPackageData(recordsPerTable);
    await insertGuestImmigrationServiceData(recordsPerTable);
    await insertGuestTrainingData(recordsPerTable + 4); // 66 to reach exactly 500

    // Final summary
    console.log('\n🎉 Payment test data insertion completed!');
    console.log('📈 Summary:');
    console.log(`   User payment tables: ${recordsPerTable * 4} records`);
    console.log(`   Guest payment tables: ${recordsPerTable * 3 + 66} records`);
    console.log(`   Total inserted: ${recordsPerTable * 7 + 66} records`);

    // Verify final counts
    const finalCounts = {
      userMentorService: await prisma.user_mentor_service.count(),
      guestMentorService: await prisma.guest_mentor_service.count(),
      userPackage: await prisma.user_package.count(),
      guestPackage: await prisma.guest_package.count(),
      userImmigrationService: await prisma.user_immigration_service.count(),
      guestImmigrationService: await prisma.guest_immigration_service.count(),
      userTraining: await prisma.user_training.count(),
      guestTraining: await prisma.guest_training.count(),
    };

    console.log('\n📊 Final table counts:');
    Object.entries(finalCounts).forEach(([table, count]) => {
      console.log(`   ${table}: ${count} records`);
    });

    const totalFinal = Object.values(finalCounts).reduce(
      (sum, count) => sum + count,
      0,
    );
    console.log(`   Total: ${totalFinal} records`);
  } catch (error) {
    console.error('❌ Error inserting payment test data:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Execute the script
main().catch((error) => {
  console.error('❌ Unhandled error:', error);
  process.exit(1);
});
