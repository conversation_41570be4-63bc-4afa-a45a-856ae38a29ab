#!/usr/bin/env ts-node

/**
 * Verification script to ensure all immigration-specific naming has been removed
 * This script scans all relevant files for immigration-specific terminology
 */

import * as fs from 'fs';
import * as path from 'path';

interface ScanResult {
  file: string;
  line: number;
  content: string;
  issue: string;
}

const results: ScanResult[] = [];

// Immigration-specific terms to search for (case-insensitive)
const immigrationTerms = [
  'immigration',
  'immigrant',
  'visa',
  'passport', // Only in specific contexts
  'work permit',
  'immigration_service',
  'immigration_workflow',
  'dynamic_immigration',
  'add_dynamic_immigration',
];

// Files to scan
const filesToScan = [
  'prisma/schema/schema.prisma',
  'prisma/schema/application.prisma',
  'prisma/schema/document.prisma',
  'prisma/schema/notification.prisma',
  'prisma/schema/user.prisma',
  'prisma/schema/payment.prisma',
  'scripts/test-dynamic-workflow-system.ts',
  'scripts/seed-workflow-data.ts',
  'scripts/rollback-dynamic-workflow-system.sql',
  'docs/DYNAMIC_WORKFLOW_SCHEMA.md',
  'docs/TASK_001_IMPLEMENTATION_SUMMARY.md',
  'docs/SECURITY_LOG_REMOVAL_SUMMARY.md',
  'docs/IMMIGRATION_NAMING_REMOVAL_SUMMARY.md',
];

// Exceptions - these are allowed contexts for certain terms
const allowedContexts = [
  'DocumentType', // Passport is a valid document type
  'enum DocumentType', // In enum definition
  'Passport', // As a document type value
  'docs/IMMIGRATION_NAMING_REMOVAL_SUMMARY.md', // This file documents the removal
  'docs/SECURITY_LOG_REMOVAL_SUMMARY.md', // May reference old names
  'user_immigration_service', // Existing system table
  'guest_immigration_service', // Existing system table
  'immigration_service?', // Existing system relationship
  'immigration_serviceId', // Existing system field
  'verify-immigration-removal.ts', // This verification script
];

function scanFile(filePath: string): void {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  File not found: ${filePath}`);
    return;
  }

  const content = fs.readFileSync(filePath, 'utf-8');
  const lines = content.split('\n');

  lines.forEach((line, index) => {
    const lineNumber = index + 1;
    const lowerLine = line.toLowerCase();

    immigrationTerms.forEach((term) => {
      if (lowerLine.includes(term.toLowerCase())) {
        // Check if this is an allowed context
        const isAllowed = allowedContexts.some((context) => {
          if (context.toLowerCase().includes(term.toLowerCase())) {
            return line.includes(context) || filePath.includes(context);
          }
          return false;
        });

        // Special handling for passport in DocumentType context
        if (term.toLowerCase() === 'passport') {
          if (
            line.includes('DocumentType') ||
            line.includes('Passport,') ||
            line.includes('Passport`')
          ) {
            return; // This is allowed
          }
        }

        // Special handling for files that document the removal process
        if (
          filePath.includes('IMMIGRATION_NAMING_REMOVAL_SUMMARY.md') ||
          filePath.includes('SECURITY_LOG_REMOVAL_SUMMARY.md') ||
          filePath.includes('verify-immigration-removal.ts')
        ) {
          return; // These files are allowed to reference old names
        }

        // Special handling for existing system references
        if (
          line.includes('user_immigration_service') ||
          line.includes('guest_immigration_service') ||
          line.includes('immigration_serviceId') ||
          (line.includes('immigration_service') && line.includes('?'))
        ) {
          return; // These are existing system references, not new workflow system
        }

        if (!isAllowed) {
          results.push({
            file: filePath,
            line: lineNumber,
            content: line.trim(),
            issue: `Found immigration-specific term: "${term}"`,
          });
        }
      }
    });
  });
}

function scanMigrationDirectories(): void {
  const migrationsDir = 'prisma/migrations';
  if (!fs.existsSync(migrationsDir)) {
    return;
  }

  const migrationDirs = fs.readdirSync(migrationsDir);
  migrationDirs.forEach((dir) => {
    if (dir.includes('immigration')) {
      results.push({
        file: `${migrationsDir}/${dir}`,
        line: 0,
        content: dir,
        issue: 'Migration directory contains "immigration" in name',
      });
    }
  });
}

function checkFileNames(): void {
  const filesToCheck = ['scripts/', 'docs/', 'prisma/schema/'];

  filesToCheck.forEach((dir) => {
    if (!fs.existsSync(dir)) return;

    const files = fs.readdirSync(dir);
    files.forEach((file) => {
      // Files that are allowed to have "immigration" in their names (documentation about removal)
      const allowedFileNames = [
        'IMMIGRATION_NAMING_REMOVAL_SUMMARY.md',
        'verify-immigration-removal.ts',
      ];

      if (
        file.toLowerCase().includes('immigration') &&
        !allowedFileNames.includes(file)
      ) {
        results.push({
          file: `${dir}${file}`,
          line: 0,
          content: file,
          issue: 'File name contains "immigration"',
        });
      }
    });
  });
}

function main(): void {
  console.log('🔍 Verifying Immigration Naming Removal...\n');

  // Scan file contents
  console.log('📄 Scanning file contents...');
  filesToScan.forEach((file) => {
    console.log(`   Scanning: ${file}`);
    scanFile(file);
  });

  // Check migration directory names
  console.log('\n📁 Checking migration directories...');
  scanMigrationDirectories();

  // Check file names
  console.log('\n📝 Checking file names...');
  checkFileNames();

  // Report results
  console.log('\n📊 Verification Results:');
  console.log('========================');

  if (results.length === 0) {
    console.log('✅ SUCCESS: No immigration-specific terminology found!');
    console.log('🎉 The system is now fully service-agnostic.');
    console.log('\n✅ Verified Components:');
    console.log('   - Schema files: Clean');
    console.log('   - Script files: Clean');
    console.log('   - Documentation: Clean');
    console.log('   - Migration names: Clean');
    console.log('   - File names: Clean');
  } else {
    console.log(
      `❌ ISSUES FOUND: ${results.length} immigration references detected\n`,
    );

    results.forEach((result, index) => {
      console.log(`${index + 1}. ${result.file}:${result.line}`);
      console.log(`   Issue: ${result.issue}`);
      console.log(`   Content: ${result.content}`);
      console.log('');
    });

    console.log(
      '🔧 Please review and update the above references to use generic terminology.',
    );
    process.exit(1);
  }

  console.log('\n🏁 Verification completed successfully!');
  console.log('The Dynamic Workflow System is ready for all service types.');
}

main();
