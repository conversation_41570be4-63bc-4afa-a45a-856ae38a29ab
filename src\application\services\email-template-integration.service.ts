import { Injectable, Logger } from '@nestjs/common';
import { render } from '@react-email/components';
import { MailerService } from '../../mailer/mailer.service';
import { LoggerService } from '../../utils/logger.service';

// Import email templates
import DocumentRequestEmail from '../../template/document-request';
import ApplicationRequirementsEmail from '../../template/application-requirements';
import ApplicationStatusChangeEmail from '../../template/application-status-change';
import DocumentRejectionEmail from '../../template/document-rejection';

// Email data interfaces
export interface DocumentRequestEmailData {
  recipientName: string;
  requesterName: string;
  applicationId?: string;
  serviceName?: string;
  documentsNeeded: string[];
  deadline?: string;
  additionalInstructions?: string;
}

export interface ApplicationRequirementsEmailData {
  applicantName: string;
  applicationId: string;
  serviceName: string;
  requiredDocuments: string[];
  websiteUrl?: string;
  applicationCreatedDate?: Date;
  isAutoGenerated?: boolean;
}

export interface ApplicationStatusChangeEmailData {
  applicantName: string;
  applicationId: string;
  serviceName: string;
  previousStatus?: string;
  currentStatus: string;
  statusChangeDate?: Date;
  nextSteps?: string[];
  additionalNotes?: string;
  websiteUrl?: string;
}

export interface DocumentRejectionEmailData {
  applicantName: string;
  applicationId: string;
  serviceName: string;
  rejectedDocuments: Array<{
    documentName: string;
    rejectionReason: string;
    specificIssues?: string[];
  }>;
  rejectionDate?: Date;
  resubmissionDeadline?: string;
  websiteUrl?: string;
  supportEmail?: string;
  generalGuidelines?: string[];
}

@Injectable()
export class EmailTemplateIntegrationService {
  private readonly logger = new Logger(EmailTemplateIntegrationService.name);

  constructor(
    private readonly mailerService: MailerService,
    private readonly loggerService: LoggerService,
  ) {}

  /**
   * Send document request email
   */
  async sendDocumentRequestEmail(
    recipientEmail: string,
    emailData: DocumentRequestEmailData,
  ): Promise<void> {
    try {
      this.logger.log(`Sending document request email to: ${recipientEmail}`);

      const html = await render(DocumentRequestEmail(emailData));

      await this.mailerService.sendEmail({
        from: process.env.EMAIL || '<EMAIL>',
        to: recipientEmail,
        subject: `Document Request - ${emailData.serviceName || 'Career Ireland'}`,
        html: html,
        cc: [],
      });

      this.loggerService.info('Document request email sent successfully', {
        recipientEmail,
        applicationId: emailData.applicationId,
        serviceName: emailData.serviceName,
      });
    } catch (error) {
      this.loggerService.error('Failed to send document request email', error, {
        recipientEmail,
        applicationId: emailData.applicationId,
      });

      // Send fallback email
      await this.sendFallbackEmail(
        recipientEmail,
        `Document Request - ${emailData.serviceName || 'Career Ireland'}`,
        this.getFallbackDocumentRequestTemplate(emailData),
      );
    }
  }

  /**
   * Send application requirements email
   */
  async sendApplicationRequirementsEmail(
    recipientEmail: string,
    emailData: ApplicationRequirementsEmailData,
  ): Promise<void> {
    try {
      this.logger.log(
        `Sending application requirements email to: ${recipientEmail}`,
      );

      const html = await render(
        ApplicationRequirementsEmail({
          ...emailData,
          websiteUrl:
            emailData.websiteUrl ||
            process.env.WEBSITE ||
            'http://localhost:3001',
        }),
      );

      await this.mailerService.sendEmail({
        from: process.env.EMAIL || '<EMAIL>',
        to: recipientEmail,
        subject: `Application Requirements - ${emailData.serviceName}`,
        html: html,
        cc: [],
      });

      this.loggerService.info(
        'Application requirements email sent successfully',
        {
          recipientEmail,
          applicationId: emailData.applicationId,
          serviceName: emailData.serviceName,
          isAutoGenerated: emailData.isAutoGenerated,
        },
      );
    } catch (error) {
      this.loggerService.error(
        'Failed to send application requirements email',
        error,
        {
          recipientEmail,
          applicationId: emailData.applicationId,
        },
      );

      // Send fallback email
      await this.sendFallbackEmail(
        recipientEmail,
        `Application Requirements - ${emailData.serviceName}`,
        this.getFallbackApplicationRequirementsTemplate(emailData),
      );
    }
  }

  /**
   * Send application status change email
   */
  async sendApplicationStatusChangeEmail(
    recipientEmail: string,
    emailData: ApplicationStatusChangeEmailData,
  ): Promise<void> {
    try {
      this.logger.log(`Sending status change email to: ${recipientEmail}`);

      const html = await render(
        ApplicationStatusChangeEmail({
          ...emailData,
          websiteUrl:
            emailData.websiteUrl ||
            process.env.WEBSITE ||
            'http://localhost:3001',
        }),
      );

      await this.mailerService.sendEmail({
        from: process.env.EMAIL || '<EMAIL>',
        to: recipientEmail,
        subject: `Application Status Update - ${emailData.serviceName}`,
        html: html,
        cc: [],
      });

      this.loggerService.info(
        'Application status change email sent successfully',
        {
          recipientEmail,
          applicationId: emailData.applicationId,
          previousStatus: emailData.previousStatus,
          currentStatus: emailData.currentStatus,
        },
      );
    } catch (error) {
      this.loggerService.error(
        'Failed to send application status change email',
        error,
        {
          recipientEmail,
          applicationId: emailData.applicationId,
        },
      );

      // Send fallback email
      await this.sendFallbackEmail(
        recipientEmail,
        `Application Status Update - ${emailData.serviceName}`,
        this.getFallbackStatusChangeTemplate(emailData),
      );
    }
  }

  /**
   * Send document rejection email
   */
  async sendDocumentRejectionEmail(
    recipientEmail: string,
    emailData: DocumentRejectionEmailData,
  ): Promise<void> {
    try {
      this.logger.log(`Sending document rejection email to: ${recipientEmail}`);

      const html = await render(
        DocumentRejectionEmail({
          ...emailData,
          websiteUrl:
            emailData.websiteUrl ||
            process.env.WEBSITE ||
            'http://localhost:3001',
          supportEmail:
            emailData.supportEmail ||
            process.env.EMAIL ||
            '<EMAIL>',
        }),
      );

      await this.mailerService.sendEmail({
        from: process.env.EMAIL || '<EMAIL>',
        to: recipientEmail,
        subject: `Document Resubmission Required - ${emailData.serviceName}`,
        html: html,
        cc: [],
      });

      this.loggerService.info('Document rejection email sent successfully', {
        recipientEmail,
        applicationId: emailData.applicationId,
        rejectedDocumentsCount: emailData.rejectedDocuments.length,
      });
    } catch (error) {
      this.loggerService.error(
        'Failed to send document rejection email',
        error,
        {
          recipientEmail,
          applicationId: emailData.applicationId,
        },
      );

      // Send fallback email
      await this.sendFallbackEmail(
        recipientEmail,
        `Document Resubmission Required - ${emailData.serviceName}`,
        this.getFallbackDocumentRejectionTemplate(emailData),
      );
    }
  }

  /**
   * Send fallback email when template rendering fails
   */
  private async sendFallbackEmail(
    recipientEmail: string,
    subject: string,
    htmlContent: string,
  ): Promise<void> {
    try {
      await this.mailerService.sendEmail({
        from: process.env.EMAIL || '<EMAIL>',
        to: recipientEmail,
        subject: subject,
        html: htmlContent,
        cc: [],
      });

      this.loggerService.info('Fallback email sent successfully', {
        recipientEmail,
        subject,
      });
    } catch (fallbackError) {
      this.loggerService.error('Failed to send fallback email', fallbackError, {
        recipientEmail,
        subject,
      });
    }
  }

  /**
   * Fallback template for document request emails
   */
  private getFallbackDocumentRequestTemplate(
    emailData: DocumentRequestEmailData,
  ): string {
    const documentsHtml = emailData.documentsNeeded
      .map((doc) => `<li>${doc}</li>`)
      .join('');

    return `
      <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2>Document Request</h2>
            <p>Dear ${emailData.recipientName},</p>
            <p>We are writing to request the submission of specific documents required for your application process.</p>

            ${emailData.applicationId ? `<p><strong>Application ID:</strong> ${emailData.applicationId}</p>` : ''}
            ${emailData.serviceName ? `<p><strong>Service:</strong> ${emailData.serviceName}</p>` : ''}

            <h3>Required Documents:</h3>
            <ul>${documentsHtml}</ul>

            ${emailData.deadline ? `<p><strong>Deadline:</strong> ${emailData.deadline}</p>` : ''}
            ${emailData.additionalInstructions ? `<p><strong>Additional Instructions:</strong> ${emailData.additionalInstructions}</p>` : ''}

            <p>If you have any questions, please don't hesitate to contact us.</p>
            <p>Best regards,<br/>${emailData.requesterName}</p>

            <hr style="margin-top: 40px; border: none; border-top: 1px solid #eee;">
            <p style="font-size: 12px; color: #666; text-align: center;">
              © ${new Date().getFullYear()} Careerireland all rights reserved.
            </p>
          </div>
        </body>
      </html>
    `;
  }

  /**
   * Fallback template for application requirements emails
   */
  private getFallbackApplicationRequirementsTemplate(
    emailData: ApplicationRequirementsEmailData,
  ): string {
    const documentsHtml = emailData.requiredDocuments
      .map((doc) => `<li>${doc}</li>`)
      .join('');

    const websiteUrl =
      emailData.websiteUrl || process.env.WEBSITE || 'http://localhost:3001';

    return `
      <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2>Application Requirements</h2>
            <p>Dear ${emailData.applicantName},</p>
            <p>Your application has been ${emailData.isAutoGenerated ? 'automatically created' : 'successfully created'} and is now ready for document submission.</p>

            <p><strong>Application ID:</strong> ${emailData.applicationId}</p>
            <p><strong>Service:</strong> ${emailData.serviceName}</p>

            <h3>Required Documents:</h3>
            <ul>${documentsHtml}</ul>

            <div style="background-color: #fef3cd; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <p><strong>⏰ Important:</strong> Your application will be completed within 7 days after all required documents are submitted.</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${websiteUrl}" style="background-color: #4f46e5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
                Login to Upload Documents
              </a>
            </div>

            <p>Thank you for choosing our services.</p>
            <p>Best regards,<br/>The Careerireland Team</p>

            <hr style="margin-top: 40px; border: none; border-top: 1px solid #eee;">
            <p style="font-size: 12px; color: #666; text-align: center;">
              © ${new Date().getFullYear()} Careerireland all rights reserved.
            </p>
          </div>
        </body>
      </html>
    `;
  }

  /**
   * Fallback template for status change emails
   */
  private getFallbackStatusChangeTemplate(
    emailData: ApplicationStatusChangeEmailData,
  ): string {
    const nextStepsHtml = emailData.nextSteps
      ? emailData.nextSteps
          .map((step, index) => `<li>${index + 1}. ${step}</li>`)
          .join('')
      : '';

    const websiteUrl =
      emailData.websiteUrl || process.env.WEBSITE || 'http://localhost:3001';

    return `
      <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2>Application Status Update</h2>
            <p>Dear ${emailData.applicantName},</p>
            <p>Your application status has been updated.</p>

            <p><strong>Application ID:</strong> ${emailData.applicationId}</p>
            <p><strong>Service:</strong> ${emailData.serviceName}</p>
            ${emailData.previousStatus ? `<p><strong>Previous Status:</strong> ${emailData.previousStatus}</p>` : ''}
            <p><strong>Current Status:</strong> <span style="color: #059669; font-weight: bold;">${emailData.currentStatus}</span></p>

            ${nextStepsHtml ? `<h3>Next Steps:</h3><ol>${nextStepsHtml}</ol>` : ''}
            ${emailData.additionalNotes ? `<div style="background-color: #f9fafb; padding: 15px; border-radius: 5px; margin: 20px 0;"><p>${emailData.additionalNotes}</p></div>` : ''}

            <div style="text-align: center; margin: 30px 0;">
              <a href="${websiteUrl}" style="background-color: #4f46e5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
                View Application Details
              </a>
            </div>

            <p>If you have any questions, please don't hesitate to contact us.</p>
            <p>Best regards,<br/>The Careerireland Team</p>

            <hr style="margin-top: 40px; border: none; border-top: 1px solid #eee;">
            <p style="font-size: 12px; color: #666; text-align: center;">
              © ${new Date().getFullYear()} Careerireland all rights reserved.
            </p>
          </div>
        </body>
      </html>
    `;
  }

  /**
   * Fallback template for document rejection emails
   */
  private getFallbackDocumentRejectionTemplate(
    emailData: DocumentRejectionEmailData,
  ): string {
    const rejectedDocsHtml = emailData.rejectedDocuments
      .map((doc) => {
        const issuesHtml = doc.specificIssues
          ? doc.specificIssues
              .map((issue) => `<li style="margin-left: 20px;">${issue}</li>`)
              .join('')
          : '';
        return `
          <div style="margin-bottom: 15px; padding: 10px; background-color: #fef2f2; border-radius: 5px;">
            <p><strong>📄 ${doc.documentName}</strong></p>
            <p><strong>Reason:</strong> ${doc.rejectionReason}</p>
            ${issuesHtml ? `<p><strong>Specific Issues:</strong></p><ul>${issuesHtml}</ul>` : ''}
          </div>
        `;
      })
      .join('');

    const websiteUrl =
      emailData.websiteUrl || process.env.WEBSITE || 'http://localhost:3001';
    const supportEmail =
      emailData.supportEmail ||
      process.env.EMAIL ||
      '<EMAIL>';

    return `
      <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2>Document Resubmission Required</h2>
            <p>Dear ${emailData.applicantName},</p>
            <p>After reviewing your submitted documents, we need to request resubmission of some documents to ensure they meet our quality standards.</p>
            <p style="color: #059669; font-style: italic;">Don't worry - this is a common part of the process, and we're here to help you get everything sorted quickly.</p>

            <p><strong>Application ID:</strong> ${emailData.applicationId}</p>
            <p><strong>Service:</strong> ${emailData.serviceName}</p>

            <h3>Documents Requiring Resubmission:</h3>
            ${rejectedDocsHtml}

            ${emailData.resubmissionDeadline ? `<div style="background-color: #fef3cd; padding: 15px; border-radius: 5px; margin: 20px 0;"><p><strong>⏰ Resubmission Deadline:</strong> ${emailData.resubmissionDeadline}</p></div>` : ''}

            <div style="text-align: center; margin: 30px 0;">
              <a href="${websiteUrl}" style="background-color: #4f46e5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
                Resubmit Documents
              </a>
            </div>

            <p>If you need any clarification, please contact us at <a href="mailto:${supportEmail}">${supportEmail}</a>.</p>
            <p>We're committed to helping you succeed.</p>
            <p>Best regards,<br/>The Careerireland Team</p>

            <hr style="margin-top: 40px; border: none; border-top: 1px solid #eee;">
            <p style="font-size: 12px; color: #666; text-align: center;">
              © ${new Date().getFullYear()} Careerireland all rights reserved.
            </p>
          </div>
        </body>
      </html>
    `;
  }
}
