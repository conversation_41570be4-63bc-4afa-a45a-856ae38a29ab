/**
 * Notification Settings Storage Service
 *
 * Simple file-based storage for notification settings using JSON files.
 * Stores all settings in a single JSON file in the config/ folder.
 *
 * <AUTHOR> Ireland Development Team
 * @version 2.0.0
 * @since 2025-07-10
 */

import { Injectable, Logger } from '@nestjs/common';
import { promises as fs } from 'fs';
import * as path from 'path';
import { LoggerService } from './logger.service';

export interface NotificationSettingsData {
  id: string;
  user_id: string;
  agent_assigned: boolean;
  case_status_update: boolean;
  agent_query: boolean;
  document_rejection: boolean;
  missing_document_reminder_days: number;
  system_maintenance: boolean;
  final_decision_issued: boolean;
  created_at: string;
  updated_at: string;
}

@Injectable()
export class NotificationSettingsStorageService {
  private readonly logger = new Logger(NotificationSettingsStorageService.name);
  private readonly configFile = path.join(
    process.cwd(),
    'config',
    'notification-settings.json',
  );

  constructor(private readonly loggerService: LoggerService) {}

  /**
   * Ensure config directory exists
   */
  private async ensureConfigDirectory(): Promise<void> {
    const configDir = path.dirname(this.configFile);
    try {
      await fs.mkdir(configDir, { recursive: true });
    } catch {
      // Directory already exists or creation failed, ignore
    }
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Get current ISO date string
   */
  private getCurrentISOString(): string {
    return new Date().toISOString();
  }

  /**
   * Read all notification settings from the single JSON file
   */
  private async readAllSettings(): Promise<
    Record<string, NotificationSettingsData>
  > {
    try {
      await this.ensureConfigDirectory();
      const data = await fs.readFile(this.configFile, 'utf8');
      return JSON.parse(data);
    } catch {
      return {}; // Return empty object if file doesn't exist or is invalid
    }
  }

  /**
   * Write all notification settings to the single JSON file
   */
  private async writeAllSettings(
    settings: Record<string, NotificationSettingsData>,
  ): Promise<void> {
    await this.ensureConfigDirectory();
    await fs.writeFile(
      this.configFile,
      JSON.stringify(settings, null, 2),
      'utf8',
    );
  }

  /**
   * Read notification settings for a specific user
   */
  async readSettings(userId: string): Promise<NotificationSettingsData | null> {
    const allSettings = await this.readAllSettings();
    return allSettings[userId] || null;
  }

  /**
   * Write notification settings for a specific user
   */
  async writeSettings(
    userId: string,
    data: NotificationSettingsData,
  ): Promise<void> {
    const allSettings = await this.readAllSettings();
    allSettings[userId] = data;
    await this.writeAllSettings(allSettings);
  }

  /**
   * Update notification settings (merge with existing)
   * This ensures PUT operations preserve existing fields
   */
  async updateSettings(
    userId: string,
    updates: Partial<NotificationSettingsData>,
  ): Promise<NotificationSettingsData> {
    const existingData = await this.readSettings(userId);

    // If no existing data, create default settings merged with updates
    if (!existingData) {
      const defaultSettings = this.createDefaultSettings(userId);
      const mergedData = {
        ...defaultSettings,
        ...updates,
        updated_at: this.getCurrentISOString(),
      } as NotificationSettingsData;

      await this.writeSettings(userId, mergedData);
      return mergedData;
    }

    // Merge updates with existing data, preserving all existing fields
    const mergedData = {
      ...existingData,
      ...updates,
      updated_at: this.getCurrentISOString(),
    } as NotificationSettingsData;

    await this.writeSettings(userId, mergedData);
    return mergedData;
  }

  /**
   * Create default notification settings
   */
  createDefaultSettings(userId: string): NotificationSettingsData {
    const currentTime = this.getCurrentISOString();
    return {
      id: this.generateId(),
      user_id: userId,
      agent_assigned: true,
      case_status_update: true,
      agent_query: true,
      document_rejection: true,
      missing_document_reminder_days: 7,
      system_maintenance: true,
      final_decision_issued: true,
      created_at: currentTime,
      updated_at: currentTime,
    };
  }

  /**
   * Delete notification settings for a user
   */
  async deleteSettings(userId: string): Promise<void> {
    const allSettings = await this.readAllSettings();
    delete allSettings[userId];
    await this.writeAllSettings(allSettings);
  }

  /**
   * Check if settings exist for a user
   */
  async settingsExist(userId: string): Promise<boolean> {
    const settings = await this.readSettings(userId);
    return settings !== null;
  }
}
